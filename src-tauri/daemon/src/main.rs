//! 企业级独立守护进程主入口
//! 
//! 提供7x24小时稳定运行的系统服务，负责：
//! - Native Messaging 代理
//! - IPC 通信服务
//! - Tauri 应用管理
//! - 企业级安全防护

use tracing::{info, error};
use anyhow::Result;

mod daemon_core;
mod config;
mod platform;
mod ipc;
mod native_messaging;
mod app_manager;
mod security;
mod monitoring;
mod error;
mod utils;

use daemon_core::SecurePasswordDaemon;
use config::DaemonConfig;
use utils::logging::init_logging;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统
    init_logging()?;
    
    info!("🚀 启动 Secure Password 守护进程 v{}", env!("CARGO_PKG_VERSION"));
    
    // 解析命令行参数
    let args = parse_command_line_args();
    
    // 加载配置文件
    let config = DaemonConfig::load_from_file(&args.config_path)
        .await
        .map_err(|e| {
            error!("配置文件加载失败: {}", e);
            e
        })?;
    
    // 验证配置
    config.validate()
        .map_err(|e| {
            error!("配置验证失败: {}", e);
            e
        })?;
    
    // 创建守护进程实例
    let mut daemon = SecurePasswordDaemon::new(config.clone())
        .await
        .map_err(|e| {
            error!("守护进程创建失败: {}", e);
            e
        })?;
    
    // 设置信号处理
    let shutdown_signal = setup_signal_handlers();
    
    // 启动守护进程
    match daemon.start().await {
        Ok(()) => {
            info!("✅ 守护进程启动成功");
            
            // 等待关闭信号
            shutdown_signal.await;
            
            info!("📥 收到关闭信号，开始优雅关闭...");
            
            // 优雅关闭
            if let Err(e) = daemon.shutdown().await {
                error!("守护进程关闭失败: {}", e);
                std::process::exit(1);
            }
            
            info!("✅ 守护进程已安全关闭");
        }
        Err(e) => {
            error!("❌ 守护进程启动失败: {}", e);
            std::process::exit(1);
        }
    }
    
    Ok(())
}

/// 命令行参数结构
#[derive(Debug)]
struct CommandLineArgs {
    config_path: String,
    log_level: String,
    daemon_mode: bool,
}

/// 解析命令行参数
fn parse_command_line_args() -> CommandLineArgs {
    use clap::{Arg, Command};
    
    let matches = Command::new("secure-password-daemon")
        .version(env!("CARGO_PKG_VERSION"))
        .about("企业级密码管理守护进程")
        .arg(
            Arg::new("config")
                .short('c')
                .long("config")
                .value_name("FILE")
                .help("配置文件路径")
                .default_value("daemon.toml")
        )
        .arg(
            Arg::new("log-level")
                .short('l')
                .long("log-level")
                .value_name("LEVEL")
                .help("日志级别 (trace, debug, info, warn, error)")
                .default_value("info")
        )
        .arg(
            Arg::new("daemon")
                .short('d')
                .long("daemon")
                .help("以守护进程模式运行")
                .action(clap::ArgAction::SetTrue)
        )
        .get_matches();

    CommandLineArgs {
        config_path: matches.get_one::<String>("config").unwrap().clone(),
        log_level: matches.get_one::<String>("log-level").unwrap().clone(),
        daemon_mode: matches.get_flag("daemon"),
    }
}

/// 设置信号处理器
async fn setup_signal_handlers() {
    #[cfg(unix)]
    {
        use tokio::signal::unix::{signal, SignalKind};
        
        let mut sigterm = signal(SignalKind::terminate()).unwrap();
        let mut sigint = signal(SignalKind::interrupt()).unwrap();
        let mut sighup = signal(SignalKind::hangup()).unwrap();
        
        tokio::select! {
            _ = sigterm.recv() => {
                info!("收到 SIGTERM 信号");
            }
            _ = sigint.recv() => {
                info!("收到 SIGINT 信号");
            }
            _ = sighup.recv() => {
                info!("收到 SIGHUP 信号");
            }
        }
    }
    
    #[cfg(windows)]
    {
        signal::ctrl_c().await.expect("无法设置 Ctrl+C 处理器");
        info!("收到 Ctrl+C 信号");
    }
}
