//! 守护进程配置定义和管理

use std::path::Path;
use serde::{Deserialize, Serialize};
use anyhow::Result;
use tracing::{info, error};

use crate::error::DaemonError;

/// 守护进程主配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DaemonConfig {
    /// 服务配置
    pub service: ServiceConfig,
    /// IPC 配置
    pub ipc: IpcConfig,
    /// Native Messaging 配置
    pub native_messaging: NativeMessagingConfig,
    /// 应用管理配置
    pub app_manager: AppManagerConfig,
    /// 安全配置
    pub security: SecurityConfig,
    /// 监控配置
    pub monitoring: MonitoringConfig,
    /// 日志配置
    pub logging: LoggingConfig,
}

/// 服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    /// 服务名称
    pub name: String,
    /// 服务显示名称
    pub display_name: String,
    /// 服务描述
    pub description: String,
    /// 是否自动启动
    pub auto_start: bool,
    /// 工作目录
    pub working_directory: Option<String>,
}

/// IPC 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcConfig {
    /// 传输类型
    pub transport: IpcTransportType,
    /// 绑定地址
    pub bind_address: String,
    /// 端口 (TCP 模式)
    pub port: Option<u16>,
    /// 最大连接数
    pub max_connections: u32,
    /// 连接超时 (秒)
    pub connection_timeout: u64,
}

/// IPC 传输类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IpcTransportType {
    /// TCP Socket
    Tcp,
    /// Unix Domain Socket
    UnixSocket,
    /// Windows Named Pipe
    NamedPipe,
    /// 自动选择
    Auto,
}

/// Native Messaging 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NativeMessagingConfig {
    /// 是否启用
    pub enabled: bool,
    /// Host 名称
    pub host_name: String,
    /// 支持的浏览器
    pub supported_browsers: Vec<String>,
    /// 扩展白名单
    pub extension_whitelist: Vec<String>,
}

/// 应用管理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppManagerConfig {
    /// 应用路径
    pub app_path: String,
    /// 启动超时 (秒)
    pub startup_timeout: u64,
    /// 健康检查间隔 (秒)
    pub health_check_interval: u64,
    /// 最大重启次数
    pub max_restart_attempts: u32,
}

/// 安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// 是否启用安全模式
    pub enabled: bool,
    /// 加密算法
    pub encryption_algorithm: String,
    /// 密钥长度
    pub key_length: u32,
}

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// 是否启用监控
    pub enabled: bool,
    /// 指标收集间隔 (秒)
    pub metrics_interval: u64,
    /// 监控端口
    pub monitoring_port: Option<u16>,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 日志文件路径
    pub file_path: Option<String>,
    /// 是否输出到控制台
    pub console: bool,
    /// 日志格式
    pub format: LogFormat,
}

/// 日志格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogFormat {
    /// 纯文本
    Text,
    /// JSON 格式
    Json,
}

impl Default for DaemonConfig {
    fn default() -> Self {
        Self {
            service: ServiceConfig {
                name: "secure-password-daemon".to_string(),
                display_name: "Secure Password Daemon".to_string(),
                description: "企业级密码管理守护进程".to_string(),
                auto_start: true,
                working_directory: None,
            },
            ipc: IpcConfig {
                transport: IpcTransportType::Auto,
                bind_address: "127.0.0.1".to_string(),
                port: Some(8080),
                max_connections: 100,
                connection_timeout: 30,
            },
            native_messaging: NativeMessagingConfig {
                enabled: true,
                host_name: "com.securepassword.host".to_string(),
                supported_browsers: vec![
                    "chrome".to_string(),
                    "firefox".to_string(),
                    "edge".to_string(),
                ],
                extension_whitelist: vec![],
            },
            app_manager: AppManagerConfig {
                app_path: "./secure-password-app".to_string(),
                startup_timeout: 60,
                health_check_interval: 30,
                max_restart_attempts: 3,
            },
            security: SecurityConfig {
                enabled: true,
                encryption_algorithm: "AES-256-GCM".to_string(),
                key_length: 256,
            },
            monitoring: MonitoringConfig {
                enabled: true,
                metrics_interval: 60,
                monitoring_port: Some(9090),
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file_path: Some("daemon.log".to_string()),
                console: true,
                format: LogFormat::Text,
            },
        }
    }
}

impl DaemonConfig {
    /// 从文件加载配置
    pub async fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self, DaemonError> {
        let path = path.as_ref();
        info!("从文件加载配置: {:?}", path);
        
        if !path.exists() {
            info!("配置文件不存在，使用默认配置");
            return Ok(Self::default());
        }
        
        let content = tokio::fs::read_to_string(path)
            .await
            .map_err(|e| DaemonError::ConfigLoadError(e.to_string()))?;
        
        let config: Self = toml::from_str(&content)
            .map_err(|e| DaemonError::ConfigParseError(e.to_string()))?;
        
        info!("配置加载成功");
        Ok(config)
    }
    
    /// 保存配置到文件
    pub async fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<(), DaemonError> {
        let path = path.as_ref();
        info!("保存配置到文件: {:?}", path);
        
        let content = toml::to_string_pretty(self)
            .map_err(|e| DaemonError::ConfigSerializeError(e.to_string()))?;
        
        tokio::fs::write(path, content)
            .await
            .map_err(|e| DaemonError::ConfigSaveError(e.to_string()))?;
        
        info!("配置保存成功");
        Ok(())
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<(), DaemonError> {
        info!("验证配置");
        
        // TODO: 实现配置验证逻辑
        
        info!("配置验证通过");
        Ok(())
    }
}
