[package]
name = "secure-password-daemon"
version = "0.1.0"
edition = "2021"
authors = ["Secure Password Team"]
description = "企业级独立守护进程 - 负责Native Messaging代理、IPC通信和应用管理"
license = "MIT"
repository = "https://github.com/your-org/secure-password"
keywords = ["daemon", "native-messaging", "ipc", "security", "password-manager"]
categories = ["authentication", "cryptography", "network-programming"]

[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }
tokio-util = "0.7"

# 序列化和反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# 日志和错误处理
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
anyhow = "1.0"
thiserror = "1.0"

# 命令行参数解析
clap = { version = "4.0", features = ["derive"] }

# 配置管理
config = "0.14"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID生成
uuid = { version = "1.0", features = ["v4", "serde"] }

# 跨平台系统API
[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_System_Services",
    "Win32_System_Registry",
    "Win32_Security",
    "Win32_System_Threading",
    "Win32_System_Pipes",
] }
winapi = { version = "0.3", features = ["winuser", "winsvc"] }

[target.'cfg(unix)'.dependencies]
nix = { version = "0.27", features = ["signal", "process", "socket"] }
libc = "0.2"

[target.'cfg(target_os = "macos")'.dependencies]
core-foundation = "0.9"
core-services = "0.2"

# 开发依赖
[dev-dependencies]
tempfile = "3.0"
serial_test = "3.0"
mockall = "0.12"

# 构建配置
[build-dependencies]
tauri-build = { version = "2.0", features = [] }
chrono = { version = "0.4", features = ["serde"] }
pkg-config = "0.3"

# 发布配置
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

# 开发配置
[profile.dev]
opt-level = 0
debug = true
overflow-checks = true

# 库目标
[lib]
name = "secure_password_daemon"
path = "src/lib.rs"

# 二进制目标
[[bin]]
name = "secure-password-daemon"
path = "src/main.rs"

# 特性标志
[features]
default = ["system-service"]
system-service = []
debug-mode = []
enterprise = ["system-service"]
