//! 守护进程集成测试

use std::time::Duration;
use tokio::time::timeout;
use tempfile::TempDir;

use secure_password_daemon::{
    config::DaemonConfig,
    daemon_core::{SecurePasswordDaemon, DaemonStatus},
    error::DaemonError,
};

/// 测试守护进程基础功能
#[tokio::test]
async fn test_daemon_basic_lifecycle() {
    // 创建临时目录用于测试
    let temp_dir = TempDir::new().expect("无法创建临时目录");
    
    // 创建测试配置
    let mut config = DaemonConfig::default();
    config.service.working_directory = Some(temp_dir.path().to_string_lossy().to_string());
    config.ipc.port = Some(0); // 使用随机端口
    
    // 创建守护进程实例
    let mut daemon = SecurePasswordDaemon::new(config)
        .await
        .expect("无法创建守护进程实例");
    
    // 测试启动
    daemon.start().await.expect("守护进程启动失败");
    
    // 验证状态
    assert_eq!(daemon.get_status(), DaemonStatus::Running);

    // 测试关闭
    daemon.shutdown().await.expect("守护进程关闭失败");

    // 验证状态
    assert_eq!(daemon.get_status(), DaemonStatus::Stopped);
}

/// 测试配置加载
#[tokio::test]
async fn test_config_loading() {
    let temp_dir = TempDir::new().expect("无法创建临时目录");
    let config_path = temp_dir.path().join("test_daemon.toml");
    
    // 创建测试配置文件
    let test_config = r#"
[service]
name = "test-daemon"
display_name = "Test Daemon"
description = "测试守护进程"
auto_start = false

[ipc]
transport = "Tcp"
bind_address = "127.0.0.1"
port = 9999
max_connections = 50
connection_timeout = 15

[native_messaging]
enabled = true
host_name = "com.test.host"
supported_browsers = ["chrome"]
extension_whitelist = []

[app_manager]
app_path = "./test-app"
startup_timeout = 60
health_check_interval = 30
max_restart_attempts = 3

[security]
enabled = true
encryption_algorithm = "AES-256-GCM"
key_length = 256

[monitoring]
enabled = true
metrics_interval = 60
monitoring_port = 9090

[logging]
level = "debug"
console = true
format = "Json"
"#;
    
    tokio::fs::write(&config_path, test_config)
        .await
        .expect("无法写入配置文件");
    
    // 加载配置
    let config = DaemonConfig::load_from_file(&config_path)
        .await
        .expect("配置加载失败");
    
    // 验证配置
    assert_eq!(config.service.name, "test-daemon");
    assert_eq!(config.service.display_name, "Test Daemon");
    assert_eq!(config.ipc.port, Some(9999));
    assert_eq!(config.ipc.max_connections, 50);
    assert_eq!(config.logging.level, "debug");
}

/// 测试配置验证
#[tokio::test]
async fn test_config_validation() {
    let config = DaemonConfig::default();
    
    // 测试默认配置验证
    assert!(config.validate().is_ok());
    
    // TODO: 添加更多配置验证测试
}

/// 测试错误处理
#[tokio::test]
async fn test_error_handling() {
    // 测试无效配置文件路径
    let result = DaemonConfig::load_from_file("/nonexistent/path/config.toml").await;
    assert!(result.is_ok()); // 应该返回默认配置
    
    // 测试无效配置内容
    let temp_dir = TempDir::new().expect("无法创建临时目录");
    let config_path = temp_dir.path().join("invalid_config.toml");
    
    tokio::fs::write(&config_path, "invalid toml content [[[")
        .await
        .expect("无法写入配置文件");
    
    let result = DaemonConfig::load_from_file(&config_path).await;
    assert!(result.is_err());
    
    match result.unwrap_err() {
        DaemonError::ConfigParseError(_) => {
            // 预期的错误类型
        }
        other => panic!("意外的错误类型: {:?}", other),
    }
}

/// 测试守护进程超时启动
#[tokio::test]
async fn test_daemon_startup_timeout() {
    let config = DaemonConfig::default();
    let mut daemon = SecurePasswordDaemon::new(config)
        .await
        .expect("无法创建守护进程实例");
    
    // 测试启动超时（设置很短的超时时间）
    let result = timeout(Duration::from_millis(1), daemon.start()).await;
    
    // 应该超时
    assert!(result.is_err());
}

/// 测试并发操作
#[tokio::test]
async fn test_concurrent_operations() {
    let config = DaemonConfig::default();
    let mut daemon = SecurePasswordDaemon::new(config)
        .await
        .expect("无法创建守护进程实例");
    
    // 启动守护进程
    daemon.start().await.expect("守护进程启动失败");
    
    // 并发执行多个操作
    let handles = (0..10).map(|_| {
        tokio::spawn(async {
            // 模拟并发操作
            tokio::time::sleep(Duration::from_millis(10)).await;
        })
    }).collect::<Vec<_>>();
    
    // 等待所有操作完成
    for handle in handles {
        handle.await.expect("任务执行失败");
    }
    
    // 关闭守护进程
    daemon.shutdown().await.expect("守护进程关闭失败");
}
