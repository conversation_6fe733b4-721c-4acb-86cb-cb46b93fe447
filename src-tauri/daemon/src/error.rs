//! 守护进程错误类型定义

use thiserror::Error;

/// 守护进程错误类型
#[derive(Error, Debug)]
pub enum DaemonError {
    /// 配置相关错误
    #[error("配置加载失败: {0}")]
    ConfigLoadError(String),
    
    #[error("配置解析失败: {0}")]
    ConfigParseError(String),
    
    #[error("配置验证失败: {0}")]
    ConfigValidationError(String),
    
    #[error("配置序列化失败: {0}")]
    ConfigSerializeError(String),
    
    #[error("配置保存失败: {0}")]
    ConfigSaveError(String),
    
    /// 服务相关错误
    #[error("服务启动失败: {0}")]
    ServiceStartError(String),
    
    #[error("服务停止失败: {0}")]
    ServiceStopError(String),
    
    #[error("服务安装失败: {0}")]
    ServiceInstallError(String),
    
    #[error("服务卸载失败: {0}")]
    ServiceUninstallError(String),
    
    /// IPC 相关错误
    #[error("IPC 服务器启动失败: {0}")]
    IpcServerStartError(String),
    
    #[error("IPC 连接失败: {0}")]
    IpcConnectionError(String),
    
    #[error("IPC 消息发送失败: {0}")]
    IpcSendError(String),
    
    #[error("IPC 消息接收失败: {0}")]
    IpcReceiveError(String),
    
    /// Native Messaging 相关错误
    #[error("Native Messaging Host 启动失败: {0}")]
    NativeMessagingStartError(String),
    
    #[error("浏览器注册失败: {0}")]
    BrowserRegistrationError(String),
    
    #[error("消息处理失败: {0}")]
    MessageHandlingError(String),
    
    /// 应用管理相关错误
    #[error("应用启动失败: {0}")]
    AppStartError(String),
    
    #[error("应用停止失败: {0}")]
    AppStopError(String),
    
    #[error("应用健康检查失败: {0}")]
    AppHealthCheckError(String),
    
    /// 安全相关错误
    #[error("安全验证失败: {0}")]
    SecurityValidationError(String),
    
    #[error("加密失败: {0}")]
    EncryptionError(String),
    
    #[error("解密失败: {0}")]
    DecryptionError(String),
    
    /// 监控相关错误
    #[error("监控系统启动失败: {0}")]
    MonitoringStartError(String),
    
    #[error("指标收集失败: {0}")]
    MetricsCollectionError(String),
    
    /// 系统相关错误
    #[error("文件系统错误: {0}")]
    FileSystemError(String),
    
    #[error("网络错误: {0}")]
    NetworkError(String),
    
    #[error("权限错误: {0}")]
    PermissionError(String),
    
    /// 通用错误
    #[error("内部错误: {0}")]
    InternalError(String),
    
    #[error("未知错误: {0}")]
    UnknownError(String),
}

/// 错误结果类型别名
pub type DaemonResult<T> = Result<T, DaemonError>;

impl From<std::io::Error> for DaemonError {
    fn from(error: std::io::Error) -> Self {
        DaemonError::FileSystemError(error.to_string())
    }
}

impl From<serde_json::Error> for DaemonError {
    fn from(error: serde_json::Error) -> Self {
        DaemonError::ConfigParseError(error.to_string())
    }
}

impl From<toml::de::Error> for DaemonError {
    fn from(error: toml::de::Error) -> Self {
        DaemonError::ConfigParseError(error.to_string())
    }
}

impl From<toml::ser::Error> for DaemonError {
    fn from(error: toml::ser::Error) -> Self {
        DaemonError::ConfigSerializeError(error.to_string())
    }
}
