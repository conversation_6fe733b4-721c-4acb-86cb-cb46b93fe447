//! 日志管理工具

use anyhow::Result;
use tracing_subscriber::{
    fmt,
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter,
};

/// 初始化日志系统
pub fn init_logging() -> Result<()> {
    // 从环境变量或默认值设置日志级别
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));
    
    // 创建格式化层
    let fmt_layer = fmt::layer()
        .with_target(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_file(true)
        .with_line_number(true);
    
    // 初始化订阅者
    tracing_subscriber::registry()
        .with(env_filter)
        .with(fmt_layer)
        .init();
    
    Ok(())
}

/// 初始化文件日志
pub fn init_file_logging(log_file: &str, level: &str) -> Result<()> {
    use std::fs::OpenOptions;
    use tracing_subscriber::fmt::writer::MakeWriterExt;
    
    // 创建日志文件写入器
    let file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(log_file)?;
    
    let env_filter = EnvFilter::new(level);
    
    // 创建文件格式化层
    let file_layer = fmt::layer()
        .with_writer(file.with_max_level(tracing::Level::TRACE))
        .with_ansi(false)
        .with_target(true)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true);
    
    // 创建控制台格式化层
    let console_layer = fmt::layer()
        .with_writer(std::io::stdout.with_max_level(tracing::Level::INFO))
        .with_target(false)
        .with_thread_ids(false)
        .with_file(false)
        .with_line_number(false);
    
    // 初始化订阅者
    tracing_subscriber::registry()
        .with(env_filter)
        .with(file_layer)
        .with(console_layer)
        .init();
    
    Ok(())
}

/// 初始化 JSON 格式日志
pub fn init_json_logging(log_file: Option<&str>) -> Result<()> {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));
    
    match log_file {
        Some(file_path) => {
            use std::fs::OpenOptions;
            use tracing_subscriber::fmt::writer::MakeWriterExt;
            
            let file = OpenOptions::new()
                .create(true)
                .append(true)
                .open(file_path)?;
            
            let file_layer = fmt::layer()
                .json()
                .with_writer(file.with_max_level(tracing::Level::TRACE))
                .with_current_span(true)
                .with_span_list(true);
            
            tracing_subscriber::registry()
                .with(env_filter)
                .with(file_layer)
                .init();
        }
        None => {
            let console_layer = fmt::layer()
                .json()
                .with_current_span(true)
                .with_span_list(true);
            
            tracing_subscriber::registry()
                .with(env_filter)
                .with(console_layer)
                .init();
        }
    }
    
    Ok(())
}
